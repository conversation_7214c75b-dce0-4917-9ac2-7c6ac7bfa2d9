package org.technoserve.udp.entity.budget;

import jakarta.persistence.Column;
import jakarta.persistence.Id;

import java.math.BigDecimal;

public class ImplementationBudgetMonthlyData {

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "category_id")
  private Long categoryId;

  @Id
  @Column(name = "year")
  private Integer year;

  @Id
  @Column(name = "month")
  private Integer month;

  @Column(name = "amount")
  private BigDecimal amount;

}
