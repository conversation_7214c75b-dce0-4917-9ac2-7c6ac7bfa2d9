package org.technoserve.udp.entity.budget;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;

@Entity
@Table(name="implementation_budget_category")
@Getter
@Setter
@NoArgsConstructor
public class ImplementationBudgetCategory extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "implementation_budget_category_id")
  private Long implementationBudgetCategoryId;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "implementation_budget_group")
  private ImplementationBudgetGroup implementationBudgetGroup;

  @Column(name = "name")
  private String name;

}
