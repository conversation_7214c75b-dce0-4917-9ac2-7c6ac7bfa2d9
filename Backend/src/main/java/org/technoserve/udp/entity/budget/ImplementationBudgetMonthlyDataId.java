package org.technoserve.udp.entity.budget;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Composite primary key for ImplementationBudgetMonthlyData entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ImplementationBudgetMonthlyDataId {

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "category_id")
  private Long categoryId;

  @Id
  @Column(name = "year")
  private Integer year;

  @Id
  @Column(name = "month")
  private Integer month;

}
