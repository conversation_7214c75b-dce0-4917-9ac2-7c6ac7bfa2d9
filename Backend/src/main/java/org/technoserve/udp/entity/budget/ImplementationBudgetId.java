package org.technoserve.udp.entity.budget;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Composite primary key for ImplementationBudget entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ImplementationBudgetId {

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "category_id")
  private Long categoryId;

}
