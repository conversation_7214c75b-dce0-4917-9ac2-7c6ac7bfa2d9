package org.technoserve.udp.entity.budget;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.technoserve.udp.entity.common.AbstractEntity;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name="implementation_budget")
@Getter
@Setter
@NoArgsConstructor
public class ImplementationBudget extends AbstractEntity{

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "category_id")
  private Long categoryId;

  @Column(name = "totalBudget")
  private BigDecimal totalBudget;


  @OneToMany(mappedBy = "implementationBudget")
  @JsonManagedReference
  private List<ImplementationBudgetMonthlyData> implementationBudgetMonthlyDataList;


}
